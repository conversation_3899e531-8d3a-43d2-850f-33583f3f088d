import {
  Controller,
  Post,
  Body,
  Res,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { ChatbotService } from './chatbot.service';
import { ChatRequestDto } from './dto/chat-message.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('chatbot')
@UseGuards(JwtAuthGuard)
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @Post('chat')
  async chat(@Body() chatRequest: ChatRequestDto, @Res() res: Response) {
    try {
      // Basic validation - we need at least one message
      if (!chatRequest.messages || chatRequest.messages.length === 0) {
        throw new HttpException(
          'Messages array is required and cannot be empty',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Process the chat request and get streaming result
      const result = await this.chatbotService.processChat(chatRequest);

      // Set headers for streaming response
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      // Pipe the stream to the response
      result.pipeDataStreamToResponse(res);
    } catch (error) {
      console.error('Chatbot error:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Internal server error while processing chat request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
