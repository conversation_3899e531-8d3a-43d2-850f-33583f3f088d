import { Drawing } from '../../../types/drawing';
import { Message } from '@ai-sdk/react';

// Chat message interface
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  contextType?: 'blueprint' | 'drawings';
}

// Context types for different chat scenarios
export interface BlueprintContext {
  imageUrl: string;
  fileName: string;
  takeoffId: string;
  blueprintFileId: string;
  blueprintImageId: string;
}

export interface DrawingsContext {
  drawings: Drawing[];
  imageUrl: string;
  takeoffId: string;
  blueprintImageId: string;
}

// AI Model types
export type AIModel = 'openai' | 'google' | 'anthropic';

export interface AIModelOption {
  value: AIModel;
  label: string;
  description: string;
}

// Chat request payload sent to backend
export interface ChatRequest {
  message: string;
  blueprintContext?: BlueprintContext;
  drawingsContext?: DrawingsContext;
  conversationHistory: ChatMessage[];
  model?: AIModel;
}

// Chat response from backend
export interface ChatResponse {
  message: string;
  contextType: 'blueprint' | 'drawings';
}

// Chatbot state interface
export interface ChatbotState {
  isOpen: boolean;
  messages: ChatMessage[];
  isLoading: boolean;
  currentContext: 'blueprint' | 'drawings' | null;
  error: string | null;
}

// Props for chatbot components
export interface ChatbotWidgetProps {
  takeoffId: string;
  projectName?: string;
}

export interface ChatbotWindowProps {
  isOpen: boolean;
  onClose: () => void;
  takeoffId: string;
  projectName?: string;
}

export interface ChatbotMessagesProps {
  messages: Message[];
  isLoading: boolean;
}

export interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  onStop?: () => void;
  isStreaming?: boolean;
}

export interface ChatbotHeaderProps {
  projectName?: string;
  onClose: () => void;
  onMinimize?: () => void;
}

// Context detection keywords
export const BLUEPRINT_KEYWORDS = [
  'door',
  'doors',
  'window',
  'windows',
  'room',
  'rooms',
  'wall',
  'walls',
  'blueprint',
  'plan',
  'floor',
  'building',
  'structure',
  'architecture',
  'layout',
  'dimension',
  'dimensions',
  'measurement',
  'measurements',
  'area',
  'square',
  'feet',
  'meter',
  'meters',
];

// AI Model options
export const AI_MODEL_OPTIONS: AIModelOption[] = [
  {
    value: 'openai',
    label: 'GPT-4o',
    description: 'OpenAI GPT-4o',
  },
  {
    value: 'google',
    label: 'Gemini 2.0',
    description: 'Google Gemini 2.0 Flash',
  },
  {
    value: 'anthropic',
    label: 'Claude 3.5',
    description: 'Anthropic Claude 3.5 Sonnet',
  },
];

export const DRAWINGS_KEYWORDS = [
  'drawing',
  'drawings',
  'annotation',
  'annotations',
  'shape',
  'shapes',
  'circle',
  'circles',
  'rectangle',
  'rectangles',
  'line',
  'lines',
  'point',
  'points',
  'arrow',
  'arrows',
  'comment',
  'comments',
  'measurement',
  'measurements',
  'markup',
  'markups',
];

// Utility function to detect context from message
export const detectContextFromMessage = (
  message: string,
): 'blueprint' | 'drawings' => {
  const lowerMessage = message.toLowerCase();

  const blueprintMatches = BLUEPRINT_KEYWORDS.filter((keyword) =>
    lowerMessage.includes(keyword),
  ).length;

  const drawingsMatches = DRAWINGS_KEYWORDS.filter((keyword) =>
    lowerMessage.includes(keyword),
  ).length;

  // If more drawing keywords, assume drawings context
  if (drawingsMatches > blueprintMatches) {
    return 'drawings';
  }

  // Default to blueprint context
  return 'blueprint';
};
