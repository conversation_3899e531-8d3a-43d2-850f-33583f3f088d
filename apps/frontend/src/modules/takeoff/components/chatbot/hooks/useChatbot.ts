import { useCallback, useMemo, useState } from 'react';
import { useChat } from '@ai-sdk/react';
import { useTakeoffStore } from '../../../store/takeoff-store';
import { useGetDrawings } from '../../../api/queries';
import {
  BlueprintContext,
  DrawingsContext,
  AIModel,
} from '../types/chatbot-types';
import { responseErrorInterceptor } from '@/lib/api-client';
import { useAuthStore } from '@/store/auth-store';

export const useChatbot = (takeoffId: string) => {
  const { selectedImage, selectedFile, setChatbotError } = useTakeoffStore();
  const token = useAuthStore((state) => state.authToken);
  const [selectedModel, setSelectedModel] = useState<AIModel>('openai');

  // Get drawings for current image
  const { data: drawingsData } = useGetDrawings(
    { blueprintImageId: selectedImage?.id || '' },
    { enabled: !!selectedImage?.id },
  );

  // Prepare context data that will be sent with each message
  const contextData = useMemo(() => {
    if (!selectedImage || !selectedFile) return null;

    const blueprintContext: BlueprintContext = {
      imageUrl: selectedImage.path,
      fileName: selectedFile.fileName,
      takeoffId: takeoffId,
      blueprintFileId: selectedFile.id,
      blueprintImageId: selectedImage.id,
    };

    const drawingsContext: DrawingsContext = {
      drawings: drawingsData || [],
      imageUrl: selectedImage.path,
      takeoffId: takeoffId,
      blueprintImageId: selectedImage.id,
    };

    return {
      blueprintContext,
      drawingsContext,
    };
  }, [selectedImage, selectedFile, takeoffId, drawingsData]);

  // Use the full power of useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    error,
    append,
    reload,
    stop,
    setMessages,
    setInput,
  } = useChat({
    api: `${process.env.NEXT_PUBLIC_BASE_URL}/chatbot/chat`,
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: contextData
      ? { contextData, model: selectedModel }
      : { model: selectedModel },
    onError: (error) => {
      const axiosError = {
        response: {
          status: JSON.parse(error.message)?.statusCode,
          data: JSON.parse(error.message),
        },
      };
      console.error(error);
      responseErrorInterceptor(axiosError, handleCustomSubmit);
    },
    onResponse: async (response) => {
      if (!response.ok) {
        setChatbotError('Failed to get response from AI assistant');
      } else {
        setChatbotError(null);
      }
    },
    onFinish: () => {
      setChatbotError(null);
    },
  });

  // Custom submit handler that includes context validation
  const handleCustomSubmit = useCallback(
    (e?: React.FormEvent, options?: { allowEmptySubmit?: boolean }) => {
      e?.preventDefault();

      if (!selectedImage || !selectedFile) {
        setChatbotError('Please select a blueprint image first');
        return;
      }

      if (!contextData) {
        setChatbotError('Context data not available');
        return;
      }

      if (!input.trim() && !options?.allowEmptySubmit) return;

      setChatbotError(null);

      // Use the standard handleSubmit with context data
      handleSubmit(e, {
        data: JSON.parse(JSON.stringify(contextData)),
      });
    },
    [
      selectedImage,
      selectedFile,
      contextData,
      input,
      handleSubmit,
      setChatbotError,
    ],
  );

  return {
    // AI SDK managed state
    messages,
    input,
    handleInputChange,
    handleSubmit: handleCustomSubmit,
    isLoading: status === 'streaming' || status === 'submitted',
    status,
    error,

    // Additional utilities
    append,
    reload,
    stop,
    setMessages,
    setInput,

    // Model selection
    selectedModel,
    setSelectedModel,

    // Context validation
    hasValidContext: !!contextData,
  };
};
