import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AIModel, AI_MODEL_OPTIONS } from './types/chatbot-types';

interface ModelSelectorProps {
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
}) => {
  return (
    <Select value={selectedModel} onValueChange={onModelChange}>
      <SelectTrigger className="w-[120px] h-8 text-xs rounded-lg border bg-background/80 hover:bg-background transition-colors shadow-sm">
        <SelectValue placeholder="Model" />
      </SelectTrigger>
      <SelectContent className="rounded-lg border shadow-lg">
        {AI_MODEL_OPTIONS.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            className="text-xs rounded focus:bg-primary/10"
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
