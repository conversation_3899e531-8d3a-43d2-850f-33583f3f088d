'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ChatbotHeader } from './ChatbotHeader';
import { ChatbotMessages } from './ChatbotMessages';
import { ChatbotInput } from './ChatbotInput';
import { ChatbotWindowProps } from './types/chatbot-types';
import { useChatbot } from './hooks/useChatbot';

export const ChatbotWindow: React.FC<ChatbotWindowProps> = ({
  isOpen,
  onClose,
  takeoffId,
  projectName,
}) => {
  const {
    messages,
    isLoading,
    error,
    input,
    handleInputChange,
    handleSubmit,
    hasValidContext,
    selectedModel,
    setSelectedModel,
    stop,
    status,
  } = useChatbot(takeoffId);

  if (!isOpen) return null;

  return (
    <>
      {/* Mobile overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-40 md:hidden"
        onClick={onClose}
      />

      {/* Chat window */}
      <div
        className={cn(
          'fixed z-50 flex flex-col transition-all duration-300 overflow-hidden',
          // Mobile: full screen
          'inset-4 md:inset-auto',
          // Desktop: bottom-right positioned
          'md:bottom-24 md:right-6 md:w-[400px] md:h-[600px]',
          // Styling
          'rounded-2xl shadow-2xl border-2 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/80',
          // Remove default padding
          'p-0',
        )}
      >
        <ChatbotHeader projectName={projectName} onClose={onClose} />

        <ChatbotMessages messages={messages} isLoading={isLoading} />

        <ChatbotInput
          input={input}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          isLoading={isLoading}
          disabled={!!error || !hasValidContext}
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          onStop={stop}
          isStreaming={status === 'streaming'}
        />

        {/* Error display */}
        {(error || !hasValidContext) && (
          <div className="px-4 py-2 bg-destructive/10 overflow-hidden">
            <p className="text-xs text-destructive break-words break-all overflow-wrap-anywhere">
              {error?.message ||
                (typeof error === 'string' ? error : error?.toString()) ||
                'Please select a blueprint image first'}
            </p>
          </div>
        )}
      </div>
    </>
  );
};
