'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AILoadingAnimationProps {
  className?: string;
}

export const AILoadingAnimation: React.FC<AILoadingAnimationProps> = ({
  className,
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {/* AI Brain Icon with pulse */}
      <div className="relative">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="text-primary animate-pulse"
        >
          <path
            d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 10.1 14.1 11 13 11S11 10.1 11 9V7.5L5 7V9C5 10.1 4.1 11 3 11S1 10.1 1 9V7C1 5.9 1.9 5 3 5H21C22.1 5 23 5.9 23 7V9C23 10.1 22.1 11 21 11S19 10.1 19 9ZM12 13C13.1 13 14 13.9 14 15V17C14 18.1 13.1 19 12 19S10 18.1 10 17V15C10 13.9 10.9 13 12 13ZM18 13C19.1 13 20 13.9 20 15V17C20 18.1 19.1 19 18 19S16 18.1 16 17V15C16 13.9 16.9 13 18 13ZM6 13C7.1 13 8 13.9 8 15V17C8 18.1 7.1 19 6 19S4 18.1 4 17V15C4 13.9 4.9 13 6 13Z"
            fill="currentColor"
          />
        </svg>

        {/* Thinking dots around the brain */}
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-ping" />
        <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-primary/70 rounded-full animate-ping animation-delay-200" />
        <div className="absolute top-0 -left-2 w-1 h-1 bg-primary/50 rounded-full animate-ping animation-delay-400" />
      </div>

      {/* Animated dots */}
      <div className="flex items-center gap-1">
        <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" />
        <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce animation-delay-200" />
        <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce animation-delay-400" />
      </div>

      {/* Text */}
      <span className="text-sm text-foreground animate-pulse">
        AI is thinking
      </span>
    </div>
  );
};
