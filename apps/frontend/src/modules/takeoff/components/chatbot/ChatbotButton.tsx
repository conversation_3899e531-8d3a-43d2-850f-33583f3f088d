'use client';

import React from 'react';
import { Bot, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ChatbotButtonProps {
  isOpen: boolean;
  onClick: () => void;
  hasUnreadMessages?: boolean;
  className?: string;
}

export const ChatbotButton: React.FC<ChatbotButtonProps> = ({
  isOpen,
  onClick,
  hasUnreadMessages = false,
  className,
}) => {
  return (
    <Button
      onClick={onClick}
      className={cn(
        'fixed bottom-6 right-6 z-50 h-auto min-h-14 px-4 py-3 rounded-full shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer',
        'bg-primary hover:bg-primary/90 text-primary-foreground',
        isOpen && 'bg-muted hover:bg-muted/90 text-muted-foreground',
        className,
      )}
      aria-label={isOpen ? 'Close chat' : 'Open chat'}
    >
      <div className="flex items-center gap-2">
        {isOpen ? (
          <X className="h-5 w-5" />
        ) : (
          <>
            <div className="relative">
              <Bot className="h-5 w-5" />
              {hasUnreadMessages && (
                <div className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full animate-pulse" />
              )}
            </div>
            <span className="text-sm font-medium">Chat with AI</span>
          </>
        )}
      </div>
    </Button>
  );
};
