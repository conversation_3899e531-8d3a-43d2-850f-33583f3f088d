import { create } from 'zustand';
// Renaming imports for clarity, as BlueprintFile in blueprint-files.ts refers to image list response
import {
  type BlueprintFilesResponse, // This is for the list of blueprint PDF/DWG files
  type BlueprintFile as BlueprintImageFileResponse, // This is for the list of images of a specific blueprint file
} from '../types/blueprint-files';
import { ComponentItemData } from '../components/CollapsibleComponentSection';
import { Drawing } from '../types/drawing';
import { UndoRedoAction } from '../types/drawing-types';
import { PaperSize, DEFAULT_PAPER_SIZE } from '@repo/component-summary';

// As per measure-tool-implementation-plan.md
export type MeasureToolVariant =
  | 'measure-rectangle'
  | 'measure-circle'
  | 'measure-ellipse'
  | 'measure-freehand-line'
  | 'measure-freehand-area'
  | 'measure-point-to-point-line'
  | 'measure-point-to-point-area'
  | 'measure-curve';

type ArrayElement<ArrayType extends readonly unknown[]> =
  ArrayType extends readonly (infer ElementType)[] ? ElementType : never;

// Type for an individual image item (derived from the response for images of a blueprint file)
export type ImageType = ArrayElement<BlueprintImageFileResponse['data']>;

// Type for an individual blueprint file item (e.g., a PDF or DWG file listed)
// This Datum is the one with fileName, fileUrl, etc.
export type BlueprintFileItemType = ArrayElement<
  BlueprintFilesResponse['data']
>;

interface ClipboardData {
  drawings: Drawing[]; // Changed to support multiple drawings
  operation: 'copy' | 'cut';
}

interface TakeoffStoreState {
  selectedImage: ImageType | null;
  setSelectedImage: (image: ImageType | null) => void;

  selectedFile: BlueprintFileItemType | null; // Added selectedFile
  setSelectedFile: (file: BlueprintFileItemType | null) => void; // Added setSelectedFile

  isCanvasLoaded: boolean; // Added isCanvasLoaded to track if the image is loaded
  setIsCanvasLoaded: (loaded: boolean) => void; // Added setIsCanvasLoaded

  // Paper size state for dynamic paper size handling
  paperSize: PaperSize;
  setPaperSize: (paperSize: PaperSize) => void;

  selectedComponentItem: ComponentItemData | null;
  setSelectedComponentItem: (item: ComponentItemData | null) => void;

  // Edit mode state
  isEditMode: boolean;
  setIsEditMode: (isEditMode: boolean) => void;

  // Drawing tooltip state
  tooltip: {
    visible: boolean;
    position: { x: number; y: number };
    drawing: Drawing | null;
  };
  setTooltip: (tooltip: {
    visible: boolean;
    position: { x: number; y: number };
    drawing: Drawing | null;
  }) => void;

  // Canvas state for coordinate transformation
  canvasState: {
    zoom: number;
    position: { x: number; y: number };
  };
  setCanvasState: (state: {
    zoom: number;
    position: { x: number; y: number };
  }) => void;

  selectedDrawingId: number | null; // Added selectedDrawingId for drawing selection
  setSelectedDrawingId: (drawingId: number | null) => void; // Added setSelectedDrawingId

  selectedDrawingIds: number[]; // Added selectedDrawingIds for multi-selection
  setSelectedDrawingIds: (drawingIds: number[]) => void; // Added setSelectedDrawingIds
  addToSelection: (drawingId: number) => void; // Add drawing to selection
  removeFromSelection: (drawingId: number) => void; // Remove drawing from selection
  clearSelection: () => void; // Clear all selections

  // Shift key state for multi-selection
  isShiftPressed: boolean;
  setIsShiftPressed: (isShiftPressed: boolean) => void;

  // Clipboard functionality
  clipboardData: ClipboardData | null;
  setClipboardData: (data: ClipboardData | null) => void;

  // Undo/Redo functionality
  undoRedoHistory: UndoRedoAction[];
  currentHistoryStep: number;
  addToHistory: (action: UndoRedoAction) => void;
  updateHistoryAction: (
    actionId: string,
    updates: Partial<UndoRedoAction>,
  ) => void;
  updateDrawingInHistory: (
    actionId: string,
    oldDrawingId: number,
    newDrawing: Drawing,
  ) => void;
  updateDrawingIdAcrossHistory: (
    oldDrawingId: number,
    newDrawing: Drawing,
  ) => void;

  moveHistoryStep: (direction: 'undo' | 'redo') => void;
  clearHistory: () => void;

  // Located component state
  locatedComponentId: number | null;
  setLocatedComponentId: (componentId: number | null) => void;

  // Measure tool state
  selectedMeasureTool: MeasureToolVariant;
  setSelectedMeasureTool: (tool: MeasureToolVariant) => void;
  isMeasureMode: boolean;
  setIsMeasureMode: (mode: boolean) => void;

  // Hidden component state for show/hide functionality
  hiddenComponentId: number | null;
  setHiddenComponentId: (componentId: number | null) => void;

  // Chatbot state (simplified - AI SDK manages messages, loading, etc.)
  chatbot: {
    isOpen: boolean;
    error: string | null; // Keep for custom validation errors
  };
  setChatbotOpen: (isOpen: boolean) => void;
  setChatbotError: (error: string | null) => void;

  reset: () => void;
}

export const useTakeoffStore = create<TakeoffStoreState>((set) => ({
  selectedImage: null,
  setSelectedImage: (image) =>
    set(() => {
      // Update paper size when image is selected and has dimensions
      if (image?.dimensions) {
        return {
          selectedImage: image,
          paperSize: {
            width: image.dimensions.width,
            height: image.dimensions.height,
            unit: image.dimensions.unit,
          },
        };
      }
      // If no dimensions, just update the selected image
      return { selectedImage: image };
    }),

  selectedFile: null, // Initialize selectedFile as null
  setSelectedFile: (file) => set({ selectedFile: file }),

  isCanvasLoaded: false, // Initialize isCanvasLoaded as false
  setIsCanvasLoaded: (loaded) => set({ isCanvasLoaded: loaded }),

  // Paper size state - default to ARCH_D
  paperSize: DEFAULT_PAPER_SIZE,
  setPaperSize: (paperSize) => set({ paperSize }),

  selectedComponentItem: null,
  setSelectedComponentItem: (item) => set({ selectedComponentItem: item }),

  // Edit mode state - default to view mode (false)
  isEditMode: true,
  setIsEditMode: (isEditMode) => set({ isEditMode }),

  // Drawing tooltip state
  tooltip: {
    visible: false,
    position: { x: 0, y: 0 },
    drawing: null,
  },
  setTooltip: (tooltip) => set({ tooltip }),

  // Canvas state for coordinate transformation
  canvasState: {
    zoom: 1,
    position: { x: 0, y: 0 },
  },
  setCanvasState: (state) => set({ canvasState: state }),

  // Measure tool state
  selectedMeasureTool: 'measure-point-to-point-line', // Default measure tool
  setSelectedMeasureTool: (tool) => set({ selectedMeasureTool: tool }),
  isMeasureMode: false, // Default to false
  setIsMeasureMode: (mode) => set({ isMeasureMode: mode }),

  selectedDrawingId: null, // Initialize selectedDrawingId as null
  setSelectedDrawingId: (drawingId) => set({ selectedDrawingId: drawingId }),

  selectedDrawingIds: [], // Initialize selectedDrawingIds as empty array
  setSelectedDrawingIds: (drawingIds) =>
    set({ selectedDrawingIds: drawingIds }),
  addToSelection: (drawingId) =>
    set((state) => ({
      selectedDrawingIds: state.selectedDrawingIds.includes(drawingId)
        ? state.selectedDrawingIds
        : [...state.selectedDrawingIds, drawingId],
    })),
  removeFromSelection: (drawingId) =>
    set((state) => ({
      selectedDrawingIds: state.selectedDrawingIds.filter(
        (id) => id !== drawingId,
      ),
    })),
  clearSelection: () => set({ selectedDrawingIds: [] }),

  // Shift key state for multi-selection
  isShiftPressed: false,
  setIsShiftPressed: (isShiftPressed) => set({ isShiftPressed }),

  // Clipboard functionality
  clipboardData: null,
  setClipboardData: (data) => set({ clipboardData: data }),

  // Undo/Redo functionality
  undoRedoHistory: [],
  currentHistoryStep: -1,
  addToHistory: (action) =>
    set((state) => {
      // Remove any actions after current step (when user does something after undoing)
      const newHistory = state.undoRedoHistory.slice(
        0,
        state.currentHistoryStep + 1,
      );

      // Add new action
      newHistory.push(action);

      // Keep only last 15 actions
      const trimmedHistory = newHistory.slice(-15);

      // Adjust current step if we trimmed from the beginning
      const newStep = trimmedHistory.length - 1;

      return {
        undoRedoHistory: trimmedHistory,
        currentHistoryStep: newStep,
      };
    }),
  updateHistoryAction: (actionId, updates) =>
    set((state) => ({
      undoRedoHistory: state.undoRedoHistory.map((action) =>
        action.id === actionId
          ? ({ ...action, ...updates } as UndoRedoAction)
          : action,
      ),
    })),
  updateDrawingInHistory: (actionId, oldDrawingId, newDrawing) =>
    set((state) => ({
      undoRedoHistory: state.undoRedoHistory.map((action) => {
        if (action.id !== actionId) return action;

        // Update drawing ID in different action types
        switch (action.type) {
          case 'create':
            return {
              ...action,
              data: {
                ...action.data,
                drawings: action.data.drawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'cut-paste':
            return {
              ...action,
              data: {
                ...action.data,
                originalDrawings: action.data.originalDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
                newDrawings: action.data.newDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'delete':
            return {
              ...action,
              data: {
                ...action.data,
                deletedDrawings: action.data.deletedDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'update':
          case 'move':
            return {
              ...action,
              data: {
                ...action.data,
                drawings: action.data.drawings.map((drawingUpdate) =>
                  drawingUpdate.id === oldDrawingId
                    ? { ...drawingUpdate, id: newDrawing.id }
                    : drawingUpdate,
                ),
              },
            } as UndoRedoAction;
          default:
            return action;
        }
      }),
    })),
  updateDrawingIdAcrossHistory: (oldDrawingId, newDrawing) =>
    set((state) => ({
      undoRedoHistory: state.undoRedoHistory.map((action) => {
        // Update drawing ID in different action types across all history
        switch (action.type) {
          case 'create':
            return {
              ...action,
              data: {
                ...action.data,
                drawings: action.data.drawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'cut-paste':
            return {
              ...action,
              data: {
                ...action.data,
                originalDrawings: action.data.originalDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
                newDrawings: action.data.newDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'delete':
            return {
              ...action,
              data: {
                ...action.data,
                deletedDrawings: action.data.deletedDrawings.map((drawing) =>
                  drawing.id === oldDrawingId ? { ...newDrawing } : drawing,
                ),
              },
            } as UndoRedoAction;
          case 'update':
          case 'move':
            return {
              ...action,
              data: {
                ...action.data,
                drawings: action.data.drawings.map((drawingUpdate) =>
                  drawingUpdate.id === oldDrawingId
                    ? { ...drawingUpdate, id: newDrawing.id }
                    : drawingUpdate,
                ),
              },
            } as UndoRedoAction;
          default:
            return action;
        }
      }),
    })),
  moveHistoryStep: (direction) =>
    set((state) => {
      if (direction === 'undo' && state.currentHistoryStep >= 0) {
        return {
          currentHistoryStep: state.currentHistoryStep - 1,
        };
      } else if (
        direction === 'redo' &&
        state.currentHistoryStep < state.undoRedoHistory.length - 1
      ) {
        return {
          currentHistoryStep: state.currentHistoryStep + 1,
        };
      }
      return state;
    }),
  clearHistory: () => set({ undoRedoHistory: [], currentHistoryStep: -1 }),

  // Located component state
  locatedComponentId: null,
  setLocatedComponentId: (componentId) =>
    set({ locatedComponentId: componentId }),

  // Hidden component state - default to null (all visible)
  hiddenComponentId: null,
  setHiddenComponentId: (componentId) =>
    set({ hiddenComponentId: componentId }),

  // Chatbot state - simplified (AI SDK manages messages, loading, etc.)
  chatbot: {
    isOpen: false,
    error: null,
  },
  setChatbotOpen: (isOpen) =>
    set((state) => ({
      chatbot: { ...state.chatbot, isOpen },
    })),
  setChatbotError: (error) =>
    set((state) => ({
      chatbot: { ...state.chatbot, error },
    })),

  reset: () => {
    set({
      selectedImage: null,
      selectedFile: null,
      selectedComponentItem: null,
      isEditMode: false,
      selectedDrawingId: null,
      selectedDrawingIds: [],
      isShiftPressed: false,
      clipboardData: null,
      undoRedoHistory: [],
      currentHistoryStep: -1,
      locatedComponentId: null,
      hiddenComponentId: null,
      paperSize: DEFAULT_PAPER_SIZE,
      selectedMeasureTool: 'measure-point-to-point-line',
      isMeasureMode: false,
      tooltip: {
        visible: false,
        position: { x: 0, y: 0 },
        drawing: null,
      },
      canvasState: {
        zoom: 1,
        position: { x: 0, y: 0 },
      },
      chatbot: {
        isOpen: false,
        error: null,
      },
    });
  },
}));
